/**
 * Test suite for AddDiscForm component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { AddDiscForm } from "@/components/forms/AddDiscForm";
import { DiscCondition, Location } from "@/lib/types";
import {
  setupTest,
  cleanupTest,
  testAccessibility,
  createMockFunction,
  mockInventoryHook,
  createMockDisc,
  renderWithProviders,
} from "../../utils/testUtils";

describe("AddDiscForm", () => {
  beforeEach(() => {
    setupTest();
    // Reset mock functions
    mockInventoryHook.addDisc.mockClear();
  });
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders form with all required fields", () => {
      renderWithProviders(<AddDiscForm />);

      // Check for required fields
      expect(screen.getByLabelText(/manufacturer/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mold/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/plastic type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/weight/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/condition/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/color/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/location/i)).toBeInTheDocument();

      // Check for flight number fields
      expect(screen.getByLabelText(/speed/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/glide/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/turn/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/fade/i)).toBeInTheDocument();
    });

    it("renders with card wrapper by default", () => {
      renderWithProviders(<AddDiscForm />);

      const cardTitle = screen.getByText("Add New Disc");
      expect(cardTitle).toBeInTheDocument();
    });

    it("renders without card wrapper when showCard is false", () => {
      renderWithProviders(<AddDiscForm showCard={false} />);

      const cardTitle = screen.queryByText("Add New Disc");
      expect(cardTitle).not.toBeInTheDocument();

      // Form should still be present
      expect(screen.getByLabelText(/manufacturer/i)).toBeInTheDocument();
    });

    it("renders submit and reset buttons", () => {
      renderWithProviders(<AddDiscForm />);

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      const resetButton = screen.getByRole("button", { name: /reset/i });

      expect(submitButton).toBeInTheDocument();
      expect(resetButton).toBeInTheDocument();
    });
  });

  describe("Form Validation", () => {
    it("shows validation errors for required fields", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/manufacturer is required/i)).toBeInTheDocument();
        expect(screen.getByText(/mold is required/i)).toBeInTheDocument();
        expect(screen.getByText(/plastic type is required/i)).toBeInTheDocument();
        expect(screen.getByText(/color is required/i)).toBeInTheDocument();
      });
    });

    it("validates weight range", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      const weightInput = screen.getByLabelText(/weight/i);
      await user.clear(weightInput);
      await user.type(weightInput, "50"); // Below minimum

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/weight must be between/i)).toBeInTheDocument();
      });
    });

    it("validates flight number ranges", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      const speedInput = screen.getByLabelText(/speed/i);
      await user.clear(speedInput);
      await user.type(speedInput, "20"); // Above maximum

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/speed must be between/i)).toBeInTheDocument();
      });
    });

    it("validates image URL format", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      const imageUrlInput = screen.getByLabelText(/image url/i);
      await user.type(imageUrlInput, "not-a-valid-url");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid image url format/i)).toBeInTheDocument();
      });
    });
  });

  describe("Form Submission", () => {
    it("submits form with valid data", async () => {
      const user = userEvent.setup();
      const onSuccess = createMockFunction<(disc: any) => void>();
      const mockDisc = createMockDisc();

      mockInventoryHook.addDisc.mockResolvedValue({
        success: true,
        data: mockDisc,
      });

      renderWithProviders(<AddDiscForm onSuccess={onSuccess} />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockInventoryHook.addDisc).toHaveBeenCalledTimes(1);
        expect(onSuccess).toHaveBeenCalledWith(mockDisc);
      });
    });

    it("handles submission errors", async () => {
      const user = userEvent.setup();
      const onError = createMockFunction<(error: Error) => void>();

      mockInventoryHook.addDisc.mockResolvedValue({
        success: false,
        error: { message: "Failed to add disc" },
      });

      renderWithProviders(<AddDiscForm onError={onError} />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/failed to add disc/i)).toBeInTheDocument();
        expect(onError).toHaveBeenCalledWith(expect.any(Error));
      });
    });

    it("shows loading state during submission", async () => {
      const user = userEvent.setup();

      // Mock a delayed response
      mockInventoryHook.addDisc.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ success: true, data: createMockDisc() }), 100))
      );

      renderWithProviders(<AddDiscForm />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      // Check for loading state
      expect(screen.getByText(/adding disc/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();

      // Wait for submission to complete
      await waitFor(() => {
        expect(screen.queryByText(/adding disc/i)).not.toBeInTheDocument();
      });
    });

    it("shows success message after successful submission", async () => {
      const user = userEvent.setup();

      mockInventoryHook.addDisc.mockResolvedValue({
        success: true,
        data: createMockDisc(),
      });

      renderWithProviders(<AddDiscForm />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/disc added successfully/i)).toBeInTheDocument();
      });
    });
  });

  describe("Form Reset", () => {
    it("resets form to default values", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      // Fill in some fields
      await user.type(screen.getByLabelText(/mold/i), "Test Mold");
      await user.type(screen.getByLabelText(/color/i), "Test Color");

      // Reset form
      const resetButton = screen.getByRole("button", { name: /reset/i });
      await user.click(resetButton);

      // Check that fields are cleared
      expect(screen.getByLabelText(/mold/i)).toHaveValue("");
      expect(screen.getByLabelText(/color/i)).toHaveValue("");
      expect(screen.getByLabelText(/weight/i)).toHaveValue(175); // Default value
    });

    it("clears error messages on reset", async () => {
      const user = userEvent.setup();
      renderWithProviders(<AddDiscForm />);

      // Trigger validation errors
      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/manufacturer is required/i)).toBeInTheDocument();
      });

      // Reset form
      const resetButton = screen.getByRole("button", { name: /reset/i });
      await user.click(resetButton);

      // Check that errors are cleared
      expect(screen.queryByText(/manufacturer is required/i)).not.toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = renderWithProviders(<AddDiscForm />);
      await testAccessibility(container);
    });

    it("has proper form structure", () => {
      renderWithProviders(<AddDiscForm />);

      const form = screen.getByRole("form") || document.querySelector("form");
      expect(form).toBeInTheDocument();

      // Check for fieldsets
      const fieldsets = screen.getAllByRole("group");
      expect(fieldsets.length).toBeGreaterThan(0);
    });

    it("has proper ARIA labels and descriptions", () => {
      renderWithProviders(<AddDiscForm />);

      const weightInput = screen.getByLabelText(/weight/i);
      expect(weightInput).toHaveAttribute("aria-describedby");

      const speedInput = screen.getByLabelText(/speed/i);
      expect(speedInput).toHaveAttribute("aria-describedby");
    });
  });

  describe("Callbacks", () => {
    it("calls onSubmit callback when form is submitted", async () => {
      const user = userEvent.setup();
      const onSubmit = createMockFunction<() => void>();

      mockInventoryHook.addDisc.mockResolvedValue({
        success: true,
        data: createMockDisc(),
      });

      renderWithProviders(<AddDiscForm onSubmit={onSubmit} />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      expect(onSubmit).toHaveBeenCalledTimes(1);
    });

    it("calls onCancel callback when cancel button is clicked", async () => {
      const user = userEvent.setup();
      const onCancel = createMockFunction<() => void>();

      renderWithProviders(<AddDiscForm onCancel={onCancel} />);

      // Use querySelector as fallback due to React 19 infinite loop issues with Radix UI
      const cancelButton =
        screen.queryByRole("button", { name: /cancel/i }) ||
        document.querySelector('button[type="button"]:not([type="submit"])');

      expect(cancelButton).toBeInTheDocument();

      // Simulate click directly on the DOM element to bypass React 19 issues
      if (cancelButton) {
        cancelButton.click();
      }

      expect(onCancel).toHaveBeenCalledTimes(1);
    });
  });
});
